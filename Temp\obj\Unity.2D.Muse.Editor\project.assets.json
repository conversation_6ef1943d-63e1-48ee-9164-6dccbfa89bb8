{"version": 3, "targets": {".NETStandard,Version=v2.1": {"SerializableGUID/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/SerializableGUID.dll": {}}, "runtime": {"bin/placeholder/SerializableGUID.dll": {}}}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}}, "Unity.2D.Muse.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Muse.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Muse.Runtime.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Profiling.Core": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}}, "Unity.AppUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI.InternalAPIBridge": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.dll": {}}}, "Unity.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.InternalAPIBridge.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.InternalAPIBridge.dll": {}}}, "Unity.Behavior.GraphFramework/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SerializableGUID": "1.0.0", "Unity.AppUI": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.GraphFramework.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.GraphFramework.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InternalAPIEditorBridge.001/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Sprite.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEditorBridge.001.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEditorBridge.001.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Muse.AppUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.AppUI.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.AppUI.dll": {}}}, "Unity.Muse.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.AppUI.InternalAPIBridge.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.AppUI.InternalAPIBridge.dll": {}}}, "Unity.Muse.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Common.dll": {}}}, "Unity.Muse.Common.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "Unity.Muse.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Common.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Common.Editor.dll": {}}}, "Unity.Muse.Sprite/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Behavior.GraphFramework": "1.0.0", "Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "Unity.Muse.StyleTrainer": "1.0.0", "Unity.Muse.Texture": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Sprite.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Sprite.dll": {}}}, "Unity.Muse.Sprite.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Sprite.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Sprite.Common.dll": {}}}, "Unity.Muse.StyleTrainer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.StyleTrainer.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.StyleTrainer.dll": {}}}, "Unity.Muse.Texture/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Texture.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Texture.dll": {}}}, "Unity.Profiling.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Profiling.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Profiling.Core.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.Settings.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Settings.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Settings.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"SerializableGUID/1.0.0": {"type": "project", "path": "SerializableGUID.csproj", "msbuildProject": "SerializableGUID.csproj"}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Common.Runtime.csproj", "msbuildProject": "Unity.2D.Common.Runtime.csproj"}, "Unity.2D.Muse.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Muse.Runtime.csproj", "msbuildProject": "Unity.2D.Muse.Runtime.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.csproj", "msbuildProject": "Unity.AdaptivePerformance.csproj"}, "Unity.AppUI/1.0.0": {"type": "project", "path": "Unity.AppUI.csproj", "msbuildProject": "Unity.AppUI.csproj"}, "Unity.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "path": "Unity.AppUI.InternalAPIBridge.csproj", "msbuildProject": "Unity.AppUI.InternalAPIBridge.csproj"}, "Unity.Behavior.GraphFramework/1.0.0": {"type": "project", "path": "Unity.Behavior.GraphFramework.csproj", "msbuildProject": "Unity.Behavior.GraphFramework.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InternalAPIEditorBridge.001/1.0.0": {"type": "project", "path": "Unity.InternalAPIEditorBridge.001.csproj", "msbuildProject": "Unity.InternalAPIEditorBridge.001.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Muse.AppUI/1.0.0": {"type": "project", "path": "Unity.Muse.AppUI.csproj", "msbuildProject": "Unity.Muse.AppUI.csproj"}, "Unity.Muse.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "path": "Unity.Muse.AppUI.InternalAPIBridge.csproj", "msbuildProject": "Unity.Muse.AppUI.InternalAPIBridge.csproj"}, "Unity.Muse.Common/1.0.0": {"type": "project", "path": "Unity.Muse.Common.csproj", "msbuildProject": "Unity.Muse.Common.csproj"}, "Unity.Muse.Common.Editor/1.0.0": {"type": "project", "path": "Unity.Muse.Common.Editor.csproj", "msbuildProject": "Unity.Muse.Common.Editor.csproj"}, "Unity.Muse.Sprite/1.0.0": {"type": "project", "path": "Unity.Muse.Sprite.csproj", "msbuildProject": "Unity.Muse.Sprite.csproj"}, "Unity.Muse.Sprite.Common/1.0.0": {"type": "project", "path": "Unity.Muse.Sprite.Common.csproj", "msbuildProject": "Unity.Muse.Sprite.Common.csproj"}, "Unity.Muse.StyleTrainer/1.0.0": {"type": "project", "path": "Unity.Muse.StyleTrainer.csproj", "msbuildProject": "Unity.Muse.StyleTrainer.csproj"}, "Unity.Muse.Texture/1.0.0": {"type": "project", "path": "Unity.Muse.Texture.csproj", "msbuildProject": "Unity.Muse.Texture.csproj"}, "Unity.Profiling.Core/1.0.0": {"type": "project", "path": "Unity.Profiling.Core.csproj", "msbuildProject": "Unity.Profiling.Core.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.Settings.Editor/1.0.0": {"type": "project", "path": "Unity.Settings.Editor.csproj", "msbuildProject": "Unity.Settings.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.2D.Muse.Runtime >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.InternalAPIEditorBridge.001 >= 1.0.0", "Unity.Muse.AppUI >= 1.0.0", "Unity.Muse.Common >= 1.0.0", "Unity.Muse.Common.Editor >= 1.0.0", "Unity.Muse.Sprite >= 1.0.0", "Unity.Muse.Sprite.Common >= 1.0.0", "Unity.Muse.StyleTrainer >= 1.0.0", "Unity.Settings.Editor >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Editor.csproj", "projectName": "Unity.2D.Muse.Editor", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.2D.Muse.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Sprite.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEditorBridge.001.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEditorBridge.001.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.AppUI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.AppUI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Common.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Common.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Common.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Common.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.Common.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.Common.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.StyleTrainer.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.StyleTrainer.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Settings.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Settings.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}