{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.2D.Animation.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Animation.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Animation.Runtime.dll": {}}}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}}, "Unity.2D.SpriteShape.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.SpriteShape.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.SpriteShape.Runtime.dll": {}}}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Profiling.Core": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}}, "Unity.AdaptivePerformance.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.Profiling.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Editor.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InternalAPIEngineBridge.001/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEngineBridge.001.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEngineBridge.001.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Profiling.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Profiling.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Profiling.Core.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.SpriteShape.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.2D.Animation.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Animation.Runtime.csproj", "msbuildProject": "Unity.2D.Animation.Runtime.csproj"}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Common.Runtime.csproj", "msbuildProject": "Unity.2D.Common.Runtime.csproj"}, "Unity.2D.SpriteShape.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.SpriteShape.Runtime.csproj", "msbuildProject": "Unity.2D.SpriteShape.Runtime.csproj"}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.csproj", "msbuildProject": "Unity.AdaptivePerformance.csproj"}, "Unity.AdaptivePerformance.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Editor.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InternalAPIEngineBridge.001/1.0.0": {"type": "project", "path": "Unity.InternalAPIEngineBridge.001.csproj", "msbuildProject": "Unity.InternalAPIEngineBridge.001.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Profiling.Core/1.0.0": {"type": "project", "path": "Unity.Profiling.Core.csproj", "msbuildProject": "Unity.Profiling.Core.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\PPv2URPConverters.csproj", "projectName": "PPv2URPConverters", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\PPv2URPConverters.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\PPv2URPConverters\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}