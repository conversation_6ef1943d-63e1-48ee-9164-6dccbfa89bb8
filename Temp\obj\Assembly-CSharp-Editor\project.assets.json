{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"PPv2URPConverters": "1.0.0", "PsdPlugin": "1.0.0", "SerializableGUID": "1.0.0", "Unity.2D.Animation.Editor": "1.0.0", "Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.Aseprite.Common": "1.0.0", "Unity.2D.Aseprite.Editor": "1.0.0", "Unity.2D.Common.Editor": "1.0.0", "Unity.2D.Common.Path.Editor": "1.0.0", "Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.IK.Editor": "1.0.0", "Unity.2D.IK.Runtime": "1.0.0", "Unity.2D.Muse.Editor": "1.0.0", "Unity.2D.Muse.Runtime": "1.0.0", "Unity.2D.PixelPerfect": "1.0.0", "Unity.2D.PixelPerfect.Editor": "1.0.0", "Unity.2D.Psdimporter.Editor": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.2D.SpriteShape.Editor": "1.0.0", "Unity.2D.SpriteShape.Runtime": "1.0.0", "Unity.2D.Tilemap.Editor": "1.0.0", "Unity.2D.Tilemap.Extras": "1.0.0", "Unity.2D.Tilemap.Extras.Editor": "1.0.0", "Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Google.Android": "1.0.0", "Unity.AdaptivePerformance.Google.Android.Editor": "1.0.0", "Unity.AdaptivePerformance.Profiler.Editor": "1.0.0", "Unity.AdaptivePerformance.Samsung.Android": "1.0.0", "Unity.AdaptivePerformance.Samsung.Android.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.Extension": "1.0.0", "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.VRR.Extension": "1.0.0", "Unity.AdaptivePerformance.UI.Editor": "1.0.0", "Unity.AppUI": "1.0.0", "Unity.AppUI.Editor": "1.0.0", "Unity.AppUI.Navigation.Editor": "1.0.0", "Unity.AppUI.Redux.Editor": "1.0.0", "Unity.Behavior": "1.0.0", "Unity.Behavior.Authoring": "1.0.0", "Unity.Behavior.Editor": "1.0.0", "Unity.Behavior.GraphFramework": "1.0.0", "Unity.Behavior.Serialization": "1.0.0", "Unity.Behavior.Serialization.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Burst.Editor": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Collections.Editor": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.InputSystem.ForUI": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.Mobile.AndroidLogcat.Editor": "1.0.0", "Unity.Multiplayer.Center.Common": "1.0.0", "Unity.Multiplayer.Center.Editor": "1.0.0", "Unity.Muse.Agent.Dynamic.Extension": "1.0.0", "Unity.Muse.Behavior": "1.0.0", "Unity.Muse.Chat.AccountHelper": "1.0.0", "Unity.Muse.Chat.BackendApi": "1.0.0", "Unity.Muse.Chat.Bridge": "1.0.0", "Unity.Muse.Sprite.Common.Editor": "1.0.0", "Unity.Muse.StyleTrainer.Editor": "1.0.0", "Unity.Notifications": "1.0.0", "Unity.Notifications.Android": "1.0.0", "Unity.Notifications.Unified": "1.0.0", "Unity.Notifications.iOS": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.Profiling.Core": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Shaders": "1.0.0", "Unity.Rendering.LightTransport.Editor": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.Sentis": "1.0.0", "Unity.Sentis.Editor": "1.0.0", "Unity.Sentis.MacBLAS": "1.0.0", "Unity.Sentis.ONNX": "1.0.0", "Unity.Sentis.iOSBLAS": "1.0.0", "Unity.Serialization": "1.0.0", "Unity.Serialization.Editor": "1.0.0", "Unity.Settings.Editor": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.SettingsProvider.Editor": "1.0.0", "Unity.VisualScripting.Shared.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "PPv2URPConverters/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PPv2URPConverters.dll": {}}, "runtime": {"bin/placeholder/PPv2URPConverters.dll": {}}}, "PsdPlugin/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PsdPlugin.dll": {}}, "runtime": {"bin/placeholder/PsdPlugin.dll": {}}}, "SerializableGUID/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/SerializableGUID.dll": {}}, "runtime": {"bin/placeholder/SerializableGUID.dll": {}}}, "Unity.2D.Animation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.Common.Editor": "1.0.0", "Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Animation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Animation.Editor.dll": {}}}, "Unity.2D.Animation.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Animation.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Animation.Runtime.dll": {}}}, "Unity.2D.Aseprite.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Sprite.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Aseprite.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Aseprite.Common.dll": {}}}, "Unity.2D.Aseprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Aseprite.Common": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.2D.Tilemap.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Aseprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Aseprite.Editor.dll": {}}}, "Unity.2D.Common.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Common.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Common.Editor.dll": {}}}, "Unity.2D.Common.Path.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Common.Path.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Common.Path.Editor.dll": {}}}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Common.Runtime.dll": {}}}, "Unity.2D.IK.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Editor": "1.0.0", "Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.Common.Editor": "1.0.0", "Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.IK.Runtime": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.IK.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.IK.Editor.dll": {}}}, "Unity.2D.IK.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.IK.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.IK.Runtime.dll": {}}}, "Unity.2D.Muse.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Muse.Runtime": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Common.Editor": "1.0.0", "Unity.Muse.Sprite": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "Unity.Muse.StyleTrainer": "1.0.0", "Unity.Settings.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Muse.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Muse.Editor.dll": {}}}, "Unity.2D.Muse.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Muse.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Muse.Runtime.dll": {}}}, "Unity.2D.PixelPerfect/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.PixelPerfect.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.PixelPerfect.dll": {}}}, "Unity.2D.PixelPerfect.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.PixelPerfect": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.PixelPerfect.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.PixelPerfect.Editor.dll": {}}}, "Unity.2D.Psdimporter.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"PsdPlugin": "1.0.0", "Unity.2D.Animation.Editor": "1.0.0", "Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.Common.Editor": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.2D.Tilemap.Editor": "1.0.0", "Unity.Burst": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "Unity.InternalAPIEngineBridge.001": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Psdimporter.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Psdimporter.Editor.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.2D.SpriteShape.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Editor": "1.0.0", "Unity.2D.Common.Path.Editor": "1.0.0", "Unity.2D.Common.Runtime": "1.0.0", "Unity.2D.SpriteShape.Runtime": "1.0.0", "Unity.InternalAPIEditorBridge.001": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.SpriteShape.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.SpriteShape.Editor.dll": {}}}, "Unity.2D.SpriteShape.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Common.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.SpriteShape.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.SpriteShape.Runtime.dll": {}}}, "Unity.2D.Tilemap.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Tilemap.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Tilemap.Editor.dll": {}}}, "Unity.2D.Tilemap.Extras/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Tilemap.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Tilemap.Extras.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Tilemap.Extras.dll": {}}}, "Unity.2D.Tilemap.Extras.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Tilemap.Editor": "1.0.0", "Unity.2D.Tilemap.Extras": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Tilemap.Extras.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Tilemap.Extras.Editor.dll": {}}}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Profiling.Core": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.dll": {}}}, "Unity.AdaptivePerformance.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.Profiling.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Editor.dll": {}}}, "Unity.AdaptivePerformance.Google.Android/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Google.Android.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Google.Android.dll": {}}}, "Unity.AdaptivePerformance.Google.Android.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Google.Android": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Google.Android.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Google.Android.Editor.dll": {}}}, "Unity.AdaptivePerformance.Profiler.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.UI.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Profiler.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Profiler.Editor.dll": {}}}, "Unity.AdaptivePerformance.Samsung.Android/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Samsung.Android.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Samsung.Android.dll": {}}}, "Unity.AdaptivePerformance.Samsung.Android.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Samsung.Android": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Samsung.Android.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Samsung.Android.Editor.dll": {}}}, "Unity.AdaptivePerformance.Simulator.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Editor.dll": {}}}, "Unity.AdaptivePerformance.Simulator.Extension/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Extension.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Extension.dll": {}}}, "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Google.Android": "1.0.0", "Unity.AdaptivePerformance.Google.Android.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll": {}}}, "Unity.AdaptivePerformance.Simulator.VRR.Extension/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.AdaptivePerformance.Samsung.Android": "1.0.0", "Unity.AdaptivePerformance.Samsung.Android.Editor": "1.0.0", "Unity.AdaptivePerformance.Simulator.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.VRR.Extension.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.Simulator.VRR.Extension.dll": {}}}, "Unity.AdaptivePerformance.UI.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AdaptivePerformance.UI.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AdaptivePerformance.UI.Editor.dll": {}}}, "Unity.AppUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI.InternalAPIBridge": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.dll": {}}}, "Unity.AppUI.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI": "1.0.0", "Unity.AppUI.InternalAPIBridge": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.Editor.dll": {}}}, "Unity.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.InternalAPIBridge.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.InternalAPIBridge.dll": {}}}, "Unity.AppUI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.Navigation.dll": {}}}, "Unity.AppUI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI": "1.0.0", "Unity.AppUI.Editor": "1.0.0", "Unity.AppUI.Navigation": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.Navigation.Editor.dll": {}}}, "Unity.AppUI.Redux/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.Redux.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.Redux.dll": {}}}, "Unity.AppUI.Redux.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AppUI": "1.0.0", "Unity.AppUI.Editor": "1.0.0", "Unity.AppUI.Redux": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AppUI.Redux.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AppUI.Redux.Editor.dll": {}}}, "Unity.Behavior/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SerializableGUID": "1.0.0", "Unity.Behavior.Serialization": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.dll": {}}}, "Unity.Behavior.Authoring/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SerializableGUID": "1.0.0", "Unity.AppUI": "1.0.0", "Unity.Behavior": "1.0.0", "Unity.Behavior.GraphFramework": "1.0.0", "Unity.Muse.Behavior": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.Authoring.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.Authoring.dll": {}}}, "Unity.Behavior.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SerializableGUID": "1.0.0", "Unity.AppUI": "1.0.0", "Unity.Behavior": "1.0.0", "Unity.Behavior.Authoring": "1.0.0", "Unity.Behavior.GraphFramework": "1.0.0", "Unity.Muse.Behavior": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.Editor.dll": {}}}, "Unity.Behavior.GraphFramework/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SerializableGUID": "1.0.0", "Unity.AppUI": "1.0.0", "Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.GraphFramework.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.GraphFramework.dll": {}}}, "Unity.Behavior.Serialization/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.Serialization.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.Serialization.dll": {}}}, "Unity.Behavior.Serialization.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Behavior.Serialization": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Behavior.Serialization.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Behavior.Serialization.Editor.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Collections.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Collections": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.Editor.dll": {}}}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}}, "Unity.InternalAPIEditorBridge.001/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Sprite.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEditorBridge.001.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEditorBridge.001.dll": {}}}, "Unity.InternalAPIEngineBridge.001/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEngineBridge.001.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEngineBridge.001.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Mobile.AndroidLogcat.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mobile.AndroidLogcat.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mobile.AndroidLogcat.Editor.dll": {}}}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Multiplayer.Center.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}}, "Unity.Muse.Agent.Dynamic.Extension/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Agent.Dynamic.Extension.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Agent.Dynamic.Extension.dll": {}}}, "Unity.Muse.AppUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.AppUI.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.AppUI.dll": {}}}, "Unity.Muse.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.AppUI.InternalAPIBridge.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.AppUI.InternalAPIBridge.dll": {}}}, "Unity.Muse.Behavior/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.Common": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Behavior.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Behavior.dll": {}}}, "Unity.Muse.Chat.AccountHelper/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Chat.AccountHelper.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Chat.AccountHelper.dll": {}}}, "Unity.Muse.Chat.BackendApi/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Chat.BackendApi.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Chat.BackendApi.dll": {}}}, "Unity.Muse.Chat.Bridge/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Chat.Bridge.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Chat.Bridge.dll": {}}}, "Unity.Muse.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Common.dll": {}}}, "Unity.Muse.Common.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "Unity.Muse.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Common.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Common.Editor.dll": {}}}, "Unity.Muse.Sprite/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Behavior.GraphFramework": "1.0.0", "Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "Unity.Muse.StyleTrainer": "1.0.0", "Unity.Muse.Texture": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Sprite.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Sprite.dll": {}}}, "Unity.Muse.Sprite.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Sprite.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Sprite.Common.dll": {}}}, "Unity.Muse.Sprite.Common.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.Sprite.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Sprite.Common.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Sprite.Common.Editor.dll": {}}}, "Unity.Muse.StyleTrainer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.StyleTrainer.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.StyleTrainer.dll": {}}}, "Unity.Muse.StyleTrainer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Muse.AppUI": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.Muse.Sprite": "1.0.0", "Unity.Muse.Sprite.Common": "1.0.0", "Unity.Muse.StyleTrainer": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.StyleTrainer.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.StyleTrainer.Editor.dll": {}}}, "Unity.Muse.Texture/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Muse.AppUI": "1.0.0", "Unity.Muse.AppUI.InternalAPIBridge": "1.0.0", "Unity.Muse.Common": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Muse.Texture.dll": {}}, "runtime": {"bin/placeholder/Unity.Muse.Texture.dll": {}}}, "Unity.Notifications/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Notifications.Android": "1.0.0", "Unity.Notifications.iOS": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Notifications.dll": {}}, "runtime": {"bin/placeholder/Unity.Notifications.dll": {}}}, "Unity.Notifications.Android/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Notifications.Android.dll": {}}, "runtime": {"bin/placeholder/Unity.Notifications.Android.dll": {}}}, "Unity.Notifications.iOS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Notifications.iOS.dll": {}}, "runtime": {"bin/placeholder/Unity.Notifications.iOS.dll": {}}}, "Unity.Notifications.Unified/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Notifications.Android": "1.0.0", "Unity.Notifications.iOS": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Notifications.Unified.dll": {}}, "runtime": {"bin/placeholder/Unity.Notifications.Unified.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Profiling.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Profiling.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Profiling.Core.dll": {}}}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.2D.SpriteShape.Runtime": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.2D.Animation.Runtime": "1.0.0", "Unity.AdaptivePerformance.Editor": "1.0.0", "Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AdaptivePerformance": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.Sentis/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Sentis.dll": {}}, "runtime": {"bin/placeholder/Unity.Sentis.dll": {}}}, "Unity.Sentis.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Sentis": "1.0.0", "Unity.Sentis.ONNX": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Sentis.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Sentis.Editor.dll": {}}}, "Unity.Sentis.iOSBLAS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Sentis": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Sentis.iOSBLAS.dll": {}}, "runtime": {"bin/placeholder/Unity.Sentis.iOSBLAS.dll": {}}}, "Unity.Sentis.MacBLAS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Sentis": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Sentis.MacBLAS.dll": {}}, "runtime": {"bin/placeholder/Unity.Sentis.MacBLAS.dll": {}}}, "Unity.Sentis.ONNX/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Sentis": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Sentis.ONNX.dll": {}}, "runtime": {"bin/placeholder/Unity.Sentis.ONNX.dll": {}}}, "Unity.Serialization/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Serialization.dll": {}}, "runtime": {"bin/placeholder/Unity.Serialization.dll": {}}}, "Unity.Serialization.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Serialization": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Serialization.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Serialization.Editor.dll": {}}}, "Unity.Settings.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Settings.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Settings.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "PPv2URPConverters/1.0.0": {"type": "project", "path": "PPv2URPConverters.csproj", "msbuildProject": "PPv2URPConverters.csproj"}, "PsdPlugin/1.0.0": {"type": "project", "path": "PsdPlugin.csproj", "msbuildProject": "PsdPlugin.csproj"}, "SerializableGUID/1.0.0": {"type": "project", "path": "SerializableGUID.csproj", "msbuildProject": "SerializableGUID.csproj"}, "Unity.2D.Animation.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Animation.Editor.csproj", "msbuildProject": "Unity.2D.Animation.Editor.csproj"}, "Unity.2D.Animation.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Animation.Runtime.csproj", "msbuildProject": "Unity.2D.Animation.Runtime.csproj"}, "Unity.2D.Aseprite.Common/1.0.0": {"type": "project", "path": "Unity.2D.Aseprite.Common.csproj", "msbuildProject": "Unity.2D.Aseprite.Common.csproj"}, "Unity.2D.Aseprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Aseprite.Editor.csproj", "msbuildProject": "Unity.2D.Aseprite.Editor.csproj"}, "Unity.2D.Common.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Common.Editor.csproj", "msbuildProject": "Unity.2D.Common.Editor.csproj"}, "Unity.2D.Common.Path.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Common.Path.Editor.csproj", "msbuildProject": "Unity.2D.Common.Path.Editor.csproj"}, "Unity.2D.Common.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Common.Runtime.csproj", "msbuildProject": "Unity.2D.Common.Runtime.csproj"}, "Unity.2D.IK.Editor/1.0.0": {"type": "project", "path": "Unity.2D.IK.Editor.csproj", "msbuildProject": "Unity.2D.IK.Editor.csproj"}, "Unity.2D.IK.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.IK.Runtime.csproj", "msbuildProject": "Unity.2D.IK.Runtime.csproj"}, "Unity.2D.Muse.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Muse.Editor.csproj", "msbuildProject": "Unity.2D.Muse.Editor.csproj"}, "Unity.2D.Muse.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.Muse.Runtime.csproj", "msbuildProject": "Unity.2D.Muse.Runtime.csproj"}, "Unity.2D.PixelPerfect/1.0.0": {"type": "project", "path": "Unity.2D.PixelPerfect.csproj", "msbuildProject": "Unity.2D.PixelPerfect.csproj"}, "Unity.2D.PixelPerfect.Editor/1.0.0": {"type": "project", "path": "Unity.2D.PixelPerfect.Editor.csproj", "msbuildProject": "Unity.2D.PixelPerfect.Editor.csproj"}, "Unity.2D.Psdimporter.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Psdimporter.Editor.csproj", "msbuildProject": "Unity.2D.Psdimporter.Editor.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.2D.SpriteShape.Editor/1.0.0": {"type": "project", "path": "Unity.2D.SpriteShape.Editor.csproj", "msbuildProject": "Unity.2D.SpriteShape.Editor.csproj"}, "Unity.2D.SpriteShape.Runtime/1.0.0": {"type": "project", "path": "Unity.2D.SpriteShape.Runtime.csproj", "msbuildProject": "Unity.2D.SpriteShape.Runtime.csproj"}, "Unity.2D.Tilemap.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Tilemap.Editor.csproj", "msbuildProject": "Unity.2D.Tilemap.Editor.csproj"}, "Unity.2D.Tilemap.Extras/1.0.0": {"type": "project", "path": "Unity.2D.Tilemap.Extras.csproj", "msbuildProject": "Unity.2D.Tilemap.Extras.csproj"}, "Unity.2D.Tilemap.Extras.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Tilemap.Extras.Editor.csproj", "msbuildProject": "Unity.2D.Tilemap.Extras.Editor.csproj"}, "Unity.AdaptivePerformance/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.csproj", "msbuildProject": "Unity.AdaptivePerformance.csproj"}, "Unity.AdaptivePerformance.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Editor.csproj"}, "Unity.AdaptivePerformance.Google.Android/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Google.Android.csproj", "msbuildProject": "Unity.AdaptivePerformance.Google.Android.csproj"}, "Unity.AdaptivePerformance.Google.Android.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Google.Android.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Google.Android.Editor.csproj"}, "Unity.AdaptivePerformance.Profiler.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Profiler.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Profiler.Editor.csproj"}, "Unity.AdaptivePerformance.Samsung.Android/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Samsung.Android.csproj", "msbuildProject": "Unity.AdaptivePerformance.Samsung.Android.csproj"}, "Unity.AdaptivePerformance.Samsung.Android.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Samsung.Android.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Samsung.Android.Editor.csproj"}, "Unity.AdaptivePerformance.Simulator.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Simulator.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Simulator.Editor.csproj"}, "Unity.AdaptivePerformance.Simulator.Extension/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Simulator.Extension.csproj", "msbuildProject": "Unity.AdaptivePerformance.Simulator.Extension.csproj"}, "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.csproj"}, "Unity.AdaptivePerformance.Simulator.VRR.Extension/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.Simulator.VRR.Extension.csproj", "msbuildProject": "Unity.AdaptivePerformance.Simulator.VRR.Extension.csproj"}, "Unity.AdaptivePerformance.UI.Editor/1.0.0": {"type": "project", "path": "Unity.AdaptivePerformance.UI.Editor.csproj", "msbuildProject": "Unity.AdaptivePerformance.UI.Editor.csproj"}, "Unity.AppUI/1.0.0": {"type": "project", "path": "Unity.AppUI.csproj", "msbuildProject": "Unity.AppUI.csproj"}, "Unity.AppUI.Editor/1.0.0": {"type": "project", "path": "Unity.AppUI.Editor.csproj", "msbuildProject": "Unity.AppUI.Editor.csproj"}, "Unity.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "path": "Unity.AppUI.InternalAPIBridge.csproj", "msbuildProject": "Unity.AppUI.InternalAPIBridge.csproj"}, "Unity.AppUI.Navigation/1.0.0": {"type": "project", "path": "Unity.AppUI.Navigation.csproj", "msbuildProject": "Unity.AppUI.Navigation.csproj"}, "Unity.AppUI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AppUI.Navigation.Editor.csproj", "msbuildProject": "Unity.AppUI.Navigation.Editor.csproj"}, "Unity.AppUI.Redux/1.0.0": {"type": "project", "path": "Unity.AppUI.Redux.csproj", "msbuildProject": "Unity.AppUI.Redux.csproj"}, "Unity.AppUI.Redux.Editor/1.0.0": {"type": "project", "path": "Unity.AppUI.Redux.Editor.csproj", "msbuildProject": "Unity.AppUI.Redux.Editor.csproj"}, "Unity.Behavior/1.0.0": {"type": "project", "path": "Unity.Behavior.csproj", "msbuildProject": "Unity.Behavior.csproj"}, "Unity.Behavior.Authoring/1.0.0": {"type": "project", "path": "Unity.Behavior.Authoring.csproj", "msbuildProject": "Unity.Behavior.Authoring.csproj"}, "Unity.Behavior.Editor/1.0.0": {"type": "project", "path": "Unity.Behavior.Editor.csproj", "msbuildProject": "Unity.Behavior.Editor.csproj"}, "Unity.Behavior.GraphFramework/1.0.0": {"type": "project", "path": "Unity.Behavior.GraphFramework.csproj", "msbuildProject": "Unity.Behavior.GraphFramework.csproj"}, "Unity.Behavior.Serialization/1.0.0": {"type": "project", "path": "Unity.Behavior.Serialization.csproj", "msbuildProject": "Unity.Behavior.Serialization.csproj"}, "Unity.Behavior.Serialization.Editor/1.0.0": {"type": "project", "path": "Unity.Behavior.Serialization.Editor.csproj", "msbuildProject": "Unity.Behavior.Serialization.Editor.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Collections.Editor/1.0.0": {"type": "project", "path": "Unity.Collections.Editor.csproj", "msbuildProject": "Unity.Collections.Editor.csproj"}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "path": "Unity.InputSystem.ForUI.csproj", "msbuildProject": "Unity.InputSystem.ForUI.csproj"}, "Unity.InternalAPIEditorBridge.001/1.0.0": {"type": "project", "path": "Unity.InternalAPIEditorBridge.001.csproj", "msbuildProject": "Unity.InternalAPIEditorBridge.001.csproj"}, "Unity.InternalAPIEngineBridge.001/1.0.0": {"type": "project", "path": "Unity.InternalAPIEngineBridge.001.csproj", "msbuildProject": "Unity.InternalAPIEngineBridge.001.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Mobile.AndroidLogcat.Editor/1.0.0": {"type": "project", "path": "Unity.Mobile.AndroidLogcat.Editor.csproj", "msbuildProject": "Unity.Mobile.AndroidLogcat.Editor.csproj"}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Common.csproj", "msbuildProject": "Unity.Multiplayer.Center.Common.csproj"}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Editor.csproj", "msbuildProject": "Unity.Multiplayer.Center.Editor.csproj"}, "Unity.Muse.Agent.Dynamic.Extension/1.0.0": {"type": "project", "path": "Unity.Muse.Agent.Dynamic.Extension.csproj", "msbuildProject": "Unity.Muse.Agent.Dynamic.Extension.csproj"}, "Unity.Muse.AppUI/1.0.0": {"type": "project", "path": "Unity.Muse.AppUI.csproj", "msbuildProject": "Unity.Muse.AppUI.csproj"}, "Unity.Muse.AppUI.InternalAPIBridge/1.0.0": {"type": "project", "path": "Unity.Muse.AppUI.InternalAPIBridge.csproj", "msbuildProject": "Unity.Muse.AppUI.InternalAPIBridge.csproj"}, "Unity.Muse.Behavior/1.0.0": {"type": "project", "path": "Unity.Muse.Behavior.csproj", "msbuildProject": "Unity.Muse.Behavior.csproj"}, "Unity.Muse.Chat.AccountHelper/1.0.0": {"type": "project", "path": "Unity.Muse.Chat.AccountHelper.csproj", "msbuildProject": "Unity.Muse.Chat.AccountHelper.csproj"}, "Unity.Muse.Chat.BackendApi/1.0.0": {"type": "project", "path": "Unity.Muse.Chat.BackendApi.csproj", "msbuildProject": "Unity.Muse.Chat.BackendApi.csproj"}, "Unity.Muse.Chat.Bridge/1.0.0": {"type": "project", "path": "Unity.Muse.Chat.Bridge.csproj", "msbuildProject": "Unity.Muse.Chat.Bridge.csproj"}, "Unity.Muse.Common/1.0.0": {"type": "project", "path": "Unity.Muse.Common.csproj", "msbuildProject": "Unity.Muse.Common.csproj"}, "Unity.Muse.Common.Editor/1.0.0": {"type": "project", "path": "Unity.Muse.Common.Editor.csproj", "msbuildProject": "Unity.Muse.Common.Editor.csproj"}, "Unity.Muse.Sprite/1.0.0": {"type": "project", "path": "Unity.Muse.Sprite.csproj", "msbuildProject": "Unity.Muse.Sprite.csproj"}, "Unity.Muse.Sprite.Common/1.0.0": {"type": "project", "path": "Unity.Muse.Sprite.Common.csproj", "msbuildProject": "Unity.Muse.Sprite.Common.csproj"}, "Unity.Muse.Sprite.Common.Editor/1.0.0": {"type": "project", "path": "Unity.Muse.Sprite.Common.Editor.csproj", "msbuildProject": "Unity.Muse.Sprite.Common.Editor.csproj"}, "Unity.Muse.StyleTrainer/1.0.0": {"type": "project", "path": "Unity.Muse.StyleTrainer.csproj", "msbuildProject": "Unity.Muse.StyleTrainer.csproj"}, "Unity.Muse.StyleTrainer.Editor/1.0.0": {"type": "project", "path": "Unity.Muse.StyleTrainer.Editor.csproj", "msbuildProject": "Unity.Muse.StyleTrainer.Editor.csproj"}, "Unity.Muse.Texture/1.0.0": {"type": "project", "path": "Unity.Muse.Texture.csproj", "msbuildProject": "Unity.Muse.Texture.csproj"}, "Unity.Notifications/1.0.0": {"type": "project", "path": "Unity.Notifications.csproj", "msbuildProject": "Unity.Notifications.csproj"}, "Unity.Notifications.Android/1.0.0": {"type": "project", "path": "Unity.Notifications.Android.csproj", "msbuildProject": "Unity.Notifications.Android.csproj"}, "Unity.Notifications.iOS/1.0.0": {"type": "project", "path": "Unity.Notifications.iOS.csproj", "msbuildProject": "Unity.Notifications.iOS.csproj"}, "Unity.Notifications.Unified/1.0.0": {"type": "project", "path": "Unity.Notifications.Unified.csproj", "msbuildProject": "Unity.Notifications.Unified.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Profiling.Core/1.0.0": {"type": "project", "path": "Unity.Profiling.Core.csproj", "msbuildProject": "Unity.Profiling.Core.csproj"}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Editor.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Editor.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Shaders.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Shaders.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.Sentis/1.0.0": {"type": "project", "path": "Unity.Sentis.csproj", "msbuildProject": "Unity.Sentis.csproj"}, "Unity.Sentis.Editor/1.0.0": {"type": "project", "path": "Unity.Sentis.Editor.csproj", "msbuildProject": "Unity.Sentis.Editor.csproj"}, "Unity.Sentis.iOSBLAS/1.0.0": {"type": "project", "path": "Unity.Sentis.iOSBLAS.csproj", "msbuildProject": "Unity.Sentis.iOSBLAS.csproj"}, "Unity.Sentis.MacBLAS/1.0.0": {"type": "project", "path": "Unity.Sentis.MacBLAS.csproj", "msbuildProject": "Unity.Sentis.MacBLAS.csproj"}, "Unity.Sentis.ONNX/1.0.0": {"type": "project", "path": "Unity.Sentis.ONNX.csproj", "msbuildProject": "Unity.Sentis.ONNX.csproj"}, "Unity.Serialization/1.0.0": {"type": "project", "path": "Unity.Serialization.csproj", "msbuildProject": "Unity.Serialization.csproj"}, "Unity.Serialization.Editor/1.0.0": {"type": "project", "path": "Unity.Serialization.Editor.csproj", "msbuildProject": "Unity.Serialization.Editor.csproj"}, "Unity.Settings.Editor/1.0.0": {"type": "project", "path": "Unity.Settings.Editor.csproj", "msbuildProject": "Unity.Settings.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.SettingsProvider.Editor.csproj", "msbuildProject": "Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Shared.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Shared.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.Editor.csproj", "msbuildProject": "Unity.VisualScripting.State.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp >= 1.0.0", "PPv2URPConverters >= 1.0.0", "PsdPlugin >= 1.0.0", "SerializableGUID >= 1.0.0", "Unity.2D.Animation.Editor >= 1.0.0", "Unity.2D.Animation.Runtime >= 1.0.0", "Unity.2D.Aseprite.Common >= 1.0.0", "Unity.2D.Aseprite.Editor >= 1.0.0", "Unity.2D.Common.Editor >= 1.0.0", "Unity.2D.Common.Path.Editor >= 1.0.0", "Unity.2D.Common.Runtime >= 1.0.0", "Unity.2D.IK.Editor >= 1.0.0", "Unity.2D.IK.Runtime >= 1.0.0", "Unity.2D.Muse.Editor >= 1.0.0", "Unity.2D.Muse.Runtime >= 1.0.0", "Unity.2D.PixelPerfect >= 1.0.0", "Unity.2D.PixelPerfect.Editor >= 1.0.0", "Unity.2D.Psdimporter.Editor >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.2D.SpriteShape.Editor >= 1.0.0", "Unity.2D.SpriteShape.Runtime >= 1.0.0", "Unity.2D.Tilemap.Editor >= 1.0.0", "Unity.2D.Tilemap.Extras >= 1.0.0", "Unity.2D.Tilemap.Extras.Editor >= 1.0.0", "Unity.AdaptivePerformance >= 1.0.0", "Unity.AdaptivePerformance.Editor >= 1.0.0", "Unity.AdaptivePerformance.Google.Android >= 1.0.0", "Unity.AdaptivePerformance.Google.Android.Editor >= 1.0.0", "Unity.AdaptivePerformance.Profiler.Editor >= 1.0.0", "Unity.AdaptivePerformance.Samsung.Android >= 1.0.0", "Unity.AdaptivePerformance.Samsung.Android.Editor >= 1.0.0", "Unity.AdaptivePerformance.Simulator.Editor >= 1.0.0", "Unity.AdaptivePerformance.Simulator.Extension >= 1.0.0", "Unity.AdaptivePerformance.Simulator.Google.Extension.Editor >= 1.0.0", "Unity.AdaptivePerformance.Simulator.VRR.Extension >= 1.0.0", "Unity.AdaptivePerformance.UI.Editor >= 1.0.0", "Unity.AppUI >= 1.0.0", "Unity.AppUI.Editor >= 1.0.0", "Unity.AppUI.Navigation.Editor >= 1.0.0", "Unity.AppUI.Redux.Editor >= 1.0.0", "Unity.Behavior >= 1.0.0", "Unity.Behavior.Authoring >= 1.0.0", "Unity.Behavior.Editor >= 1.0.0", "Unity.Behavior.GraphFramework >= 1.0.0", "Unity.Behavior.Serialization >= 1.0.0", "Unity.Behavior.Serialization.Editor >= 1.0.0", "Unity.Burst >= 1.0.0", "Unity.Burst.Editor >= 1.0.0", "Unity.Collections >= 1.0.0", "Unity.Collections.Editor >= 1.0.0", "Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.InputSystem >= 1.0.0", "Unity.InputSystem.ForUI >= 1.0.0", "Unity.InternalAPIEditorBridge.001 >= 1.0.0", "Unity.InternalAPIEngineBridge.001 >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.Mobile.AndroidLogcat.Editor >= 1.0.0", "Unity.Multiplayer.Center.Common >= 1.0.0", "Unity.Multiplayer.Center.Editor >= 1.0.0", "Unity.Muse.Agent.Dynamic.Extension >= 1.0.0", "Unity.Muse.Behavior >= 1.0.0", "Unity.Muse.Chat.AccountHelper >= 1.0.0", "Unity.Muse.Chat.BackendApi >= 1.0.0", "Unity.Muse.Chat.Bridge >= 1.0.0", "Unity.Muse.Sprite.Common.Editor >= 1.0.0", "Unity.Muse.StyleTrainer.Editor >= 1.0.0", "Unity.Notifications >= 1.0.0", "Unity.Notifications.Android >= 1.0.0", "Unity.Notifications.Unified >= 1.0.0", "Unity.Notifications.iOS >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.Profiling.Core >= 1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor.Shared >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Shaders >= 1.0.0", "Unity.Rendering.LightTransport.Editor >= 1.0.0", "Unity.Rendering.LightTransport.Runtime >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.Sentis >= 1.0.0", "Unity.Sentis.Editor >= 1.0.0", "Unity.Sentis.MacBLAS >= 1.0.0", "Unity.Sentis.ONNX >= 1.0.0", "Unity.Sentis.iOSBLAS >= 1.0.0", "Unity.Serialization >= 1.0.0", "Unity.Serialization.Editor >= 1.0.0", "Unity.Settings.Editor >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.SettingsProvider.Editor >= 1.0.0", "Unity.VisualScripting.Shared.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "Unity.VisualScripting.State.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Assembly-CSharp.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Assembly-CSharp.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\PPv2URPConverters.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\PPv2URPConverters.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\PsdPlugin.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\PsdPlugin.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\SerializableGUID.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\SerializableGUID.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Animation.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Animation.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Animation.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Animation.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Aseprite.Common.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Aseprite.Common.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Aseprite.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Aseprite.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Path.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Path.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Common.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.IK.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.IK.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.IK.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.IK.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Muse.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.PixelPerfect.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.PixelPerfect.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.PixelPerfect.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.PixelPerfect.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Psdimporter.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Psdimporter.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Sprite.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.SpriteShape.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.SpriteShape.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.SpriteShape.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.SpriteShape.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Extras.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Extras.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Extras.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.2D.Tilemap.Extras.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Profiler.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Profiler.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Samsung.Android.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Samsung.Android.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Samsung.Android.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Samsung.Android.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Extension.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Extension.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.VRR.Extension.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Simulator.VRR.Extension.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Navigation.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Navigation.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Redux.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AppUI.Redux.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Authoring.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Authoring.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.GraphFramework.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.GraphFramework.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Serialization.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Serialization.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Serialization.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Behavior.Serialization.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Burst.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Burst.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Burst.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Burst.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Collections.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Collections.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Collections.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Collections.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.EditorCoroutines.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.ForUI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.ForUI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEditorBridge.001.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEditorBridge.001.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEngineBridge.001.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InternalAPIEngineBridge.001.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mathematics.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mathematics.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mathematics.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mathematics.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mobile.AndroidLogcat.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Mobile.AndroidLogcat.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Multiplayer.Center.Common.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Multiplayer.Center.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Agent.Dynamic.Extension.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Agent.Dynamic.Extension.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Behavior.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Behavior.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.AccountHelper.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.AccountHelper.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.BackendApi.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.BackendApi.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.Bridge.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Chat.Bridge.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.Common.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.Sprite.Common.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.StyleTrainer.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Muse.StyleTrainer.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.Android.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.Android.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.iOS.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.iOS.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.Unified.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Notifications.Unified.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.PlasticSCM.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rendering.LightTransport.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rendering.LightTransport.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rider.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Rider.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Searcher.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Searcher.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.iOSBLAS.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.iOSBLAS.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.MacBLAS.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.MacBLAS.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.ONNX.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Sentis.ONNX.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Serialization.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Serialization.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Serialization.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Serialization.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Settings.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Settings.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.ShaderGraph.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.TextMeshPro.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.TextMeshPro.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.TextMeshPro.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Timeline.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Timeline.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Timeline.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Timeline.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Shared.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.State.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.State.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.State.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualStudio.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualStudio.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}