{"format": 1, "restore": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj": {}}, "projects": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj", "projectName": "Unity.AdaptivePerformance.UI.Editor", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.UI.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.AdaptivePerformance.UI.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}}