{"format": 1, "restore": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj": {}}, "projects": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj", "projectName": "Unity.AdaptivePerformance", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.AdaptivePerformance\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj", "projectName": "Unity.AdaptivePerformance.Editor", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.AdaptivePerformance.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj", "projectName": "Unity.AdaptivePerformance.Google.Android", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.AdaptivePerformance.Google.Android\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj", "projectName": "Unity.AdaptivePerformance.Google.Android.Editor", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.AdaptivePerformance.Google.Android.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Editor.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.AdaptivePerformance.Google.Android.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj", "projectName": "Unity.InputSystem", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.InputSystem\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj", "projectName": "Unity.Profiling.Core", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.Profiling.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.Profiling.Core\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj", "projectName": "Unity.VisualScripting.Core", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.VisualScripting.Core\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj", "projectName": "Unity.VisualScripting.Flow", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Flow.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\Unity.VisualScripting.Flow\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.InputSystem.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\Unity.VisualScripting.Core.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEditor.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj"}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}, "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "b:\\Unity\\Spincraft\\SpinCraft\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "b:\\Unity\\Spincraft\\SpinCraft\\Temp\\obj\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}}}}